<?php

namespace Drupal\scraper_news\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\file\FileRepositoryInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\media\Entity\Media;

/**
 * Provides a form for scraping news articles.
 */
class ScraperNewsForm extends FormBase {
  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The file repository service.
   *
   * @var \Drupal\file\FileRepositoryInterface
   */
  protected $fileRepository;

  /**
   * URLs de base pour les actualités par secteur.
   */
  private const NEWS_URLS = [
    'transport_routier' => [
      'fr' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'ferroviaire' => [
      'fr' => 'https://www.transport.gov.ma/ferroviaire/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/ferroviaire/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'logistique' => [
      'fr' => 'https://www.transport.gov.ma/logistique/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/logistique/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'maritime' => [
      'fr' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'default' => [
      'fr' => 'https://www.transport.gov.ma/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/AR/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
  ];

 /**
   * IDs des divs contenant les articles selon le secteur et la langue.
   */
  private const NEWS_DIV_IDS = [
    'transport_routier' => [
      'fr' => 'ctl00_SPWebPartManager1_g_239181c7_58a5_4b48_a074_b30219232440_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_23c3742f_cb02_4c0e_b0a4_01469c664a0c_ctl00_detailPnl',
    ],
    'maritime' => [
      'fr' => 'ctl00_SPWebPartManager1_g_0576a896_ace3_45ca_b76f_a0e81d54070c_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_b5beef63_d932_4292_8510_fbf7a4675e8a_ctl00_detailPnl',
    ],
    'logistique' => [
      'fr' => 'ctl00_SPWebPartManager1_g_b6661ef5_e4dd_4ddf_917c_7bb6efbeffb5_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_00cdbca3_509a_43ae_9489_991f998ac57f_ctl00_detailPnl',
    ],
    'ferroviaire' => [
      'fr' => 'ctl00_SPWebPartManager1_g_accb643e_125d_457a_a645_b8e90112138e_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_1a1966cf_7558_4e82_b385_69f9f0b61944_ctl00_detailPnl',
    ],
    'default' => [
      'fr' => 'ctl00_SPWebPartManager1_g_1b059c32_8d39_44a2_aea7_47c3d33fd6ad_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_562ad89e_a8c9_4be8_96ce_f25d5c6214f4_ctl00_detailPnl',
    ],
  ];

  /**
   * Constructs a new ScraperNewsForm.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    FileRepositoryInterface $file_repository
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->fileRepository = $file_repository;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('file_system'),
      $container->get('file.repository')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'scraper_news_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    // Charger les termes de taxonomie secteur
    $secteur_options = $this->getSecteurOptions();

    $form['secteur'] = [
      '#type' => 'select',
      '#title' => $this->t('Secteur'),
      '#options' => $secteur_options,
      '#required' => TRUE,
      '#default_value' => 'default',
    ];

    $form['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue'),
      '#options' => [
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
      ],
      '#required' => TRUE,
      '#default_value' => 'fr',
    ];

    $form['id_start'] = [
      '#type' => 'number',
      '#title' => $this->t('ID de départ'),
      '#required' => TRUE,
      '#min' => 1,
    ];

    $form['id_end'] = [
      '#type' => 'number',
      '#title' => $this->t('ID de fin'),
      '#required' => TRUE,
      '#min' => 1,
    ];

    $form['actions']['#type'] = 'actions';
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Scraper les actualités'),
      '#button_type' => 'primary',
    ];
    return $form;
  }

  /**
   * Récupère les options de secteur pour le formulaire.
   */
  private function getSecteurOptions() {
    $options = ['default' => $this->t('Secteur général')];

    try {
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties(['vid' => 'modes_de_transport']);

      foreach ($terms as $term) {
        // Ajouter tous les termes, même ceux sans liens valides
        $term_name = strtolower($term->label());
        $url_key = $this->mapTermToUrlKey($term_name);
        if ($url_key) {
          $options[$url_key] = $term->label();
        } else {
          // Utiliser l'ID du terme comme clé pour les secteurs sans liens
          $options['term_' . $term->id()] = $term->label();
        }
      }
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error loading secteur terms: @message', [
        '@message' => $e->getMessage(),
      ]);
    }

    return $options;
  }

  /**
   * Mappe les noms de termes aux clés d'URL.
   */
  private function mapTermToUrlKey($term_name) {
    $mapping = [
      'transport routier' => 'transport_routier',
      'transport ferroviaire' => 'ferroviaire',
      'logistique' => 'logistique',
      'marine marchande' => 'maritime',
    ];

    return $mapping[$term_name] ?? null;
  }

  /**
   * Récupère le terme de taxonomie secteur par clé de secteur.
   */
  private function getSecteurTermBySecteur($secteur) {
    try {
      // Si c'est un terme avec ID (term_123), extraire l'ID
      if (str_starts_with($secteur, 'term_')) {
        $term_id = str_replace('term_', '', $secteur);
        return $this->entityTypeManager
          ->getStorage('taxonomy_term')
          ->load($term_id);
      }

      // Sinon, utiliser le mapping pour les secteurs avec liens
      $reverse_mapping = [
        'transport_routier' => 'Transport routier',
        'ferroviaire' => 'Transport ferroviaire',
        'logistique' => 'Logistique',
        'maritime' => 'Marine marchande',
      ];

      $term_name = $reverse_mapping[$secteur] ?? null;
      if (!$term_name) {
        \Drupal::logger('scraper_news')->debug('No mapping found for secteur: @secteur', [
          '@secteur' => $secteur,
        ]);
        return null;
      }

      \Drupal::logger('scraper_news')->debug('Looking for term with name: @name in vocabulary modes_de_transport', [
        '@name' => $term_name,
      ]);

      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'vid' => 'modes_de_transport',
          'name' => $term_name,
        ]);

      if (!empty($terms)) {
        $term = reset($terms);
        \Drupal::logger('scraper_news')->debug('Found term: @name (ID: @id)', [
          '@name' => $term->label(),
          '@id' => $term->id(),
        ]);
        return $term;
      } else {
        \Drupal::logger('scraper_news')->warning('No term found with name: @name in vocabulary modes_de_transport', [
          '@name' => $term_name,
        ]);
        return null;
      }
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error loading secteur term: @message', [
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $id_start = $form_state->getValue('id_start');
    $id_end = $form_state->getValue('id_end');
    $language = $form_state->getValue('language');
    $secteur = $form_state->getValue('secteur');

    $batch = [
      'title' => $this->t('Importing news articles...'),
      'operations' => [],
      'finished' => [$this, 'batchFinished'],
      'progress_message' => $this->t('Processed @current out of @total articles.'),
    ];

    for ($id = $id_start; $id <= $id_end; $id++) {
      $batch['operations'][] = [
        [$this, 'processArticle'],
        [$id, $language, $secteur],
      ];
    }

    batch_set($batch);
  }

  /**
   * Processes a single article for batch operations.
   */
  public function processArticle($id, $language, $secteur = 'default', &$context) {
    // Log pour debug
    \Drupal::logger('scraper_news')->debug('processArticle called with: ID=@id, Language=@lang, Secteur=@secteur', [
      '@id' => $id,
      '@lang' => $language,
      '@secteur' => $secteur,
    ]);

    // Initialiser le contexte de manière robuste
    if (!isset($context['results']) || !is_array($context['results'])) {
      $context['results'] = [
        'success' => 0,
        'skipped' => 0,
        'errors' => 0,
      ];
    }

    // S'assurer que toutes les clés existent
    $context['results']['success'] = $context['results']['success'] ?? 0;
    $context['results']['skipped'] = $context['results']['skipped'] ?? 0;
    $context['results']['errors'] = $context['results']['errors'] ?? 0;

    // Vérifier si le secteur a des URLs valides
    if (str_starts_with($secteur, 'term_') || !isset(self::NEWS_URLS[$secteur])) {
      $context['message'] = $this->t('Import non disponible pour ce secteur - aucun lien valide configuré');
      $context['results']['skipped']++;
      return;
    }

    $base_url = self::NEWS_URLS[$secteur][$language];

    $url = $base_url . $id;

    \Drupal::logger('scraper_news')->debug('Fetching URL: @url', ['@url' => $url]);

    $html = $this->fetchHtml($url);
    if ($html) {
      \Drupal::logger('scraper_news')->debug('HTML fetched successfully, length: @length', ['@length' => strlen($html)]);

      $data = $this->parseHtml($html, $language);
      \Drupal::logger('scraper_news')->debug('Parsed data count: @count', ['@count' => count($data)]);

      if (!empty($data)) {
        \Drupal::logger('scraper_news')->debug('First item data: @data', [
          '@data' => print_r($data[0] ?? [], TRUE)
        ]);
      }

      if (!empty($data) && !empty($data[0]['title']) && !empty($data[0]['body'])) {
        foreach ($data as $item) {
          $this->createNewsNode($item, $language, $secteur);
          $context['results']['success']++;
        }
      } else {
        \Drupal::logger('scraper_news')->warning('No valid data found or missing title/body');
        $context['results']['skipped']++;
      }
    } else {
      \Drupal::logger('scraper_news')->error('Failed to fetch HTML from URL: @url', ['@url' => $url]);
      $context['results']['skipped']++;
    }

    $context['message'] = $this->t('Processing article @id for @secteur', ['@id' => $id, '@secteur' => $secteur]);
  }

  /**
   * Récupère le contenu HTML d'une URL.
   */
  private function fetchHtml($url) {
    try {
      // Validation simple de l'URL
      if (!filter_var($url, FILTER_VALIDATE_URL)) {
        throw new \Exception('Invalid URL provided');
      }

      $ch = curl_init();
      if ($ch === false) {
        throw new \Exception('Failed to initialize cURL');
      }

      $options = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_USERAGENT => 'Drupal Scraper Bot',
      ];
      
      curl_setopt_array($ch, $options);
      $html = curl_exec($ch);
      
      if ($html === false) {
        throw new \Exception(curl_error($ch));
      }
      
      $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
      if ($httpCode !== 200) {
        throw new \Exception("HTTP request failed with status $httpCode");
      }

      return $html;
    }
    catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error fetching URL @url: @message', [
        '@url' => $url,
        '@message' => $e->getMessage(),
      ]);
      return false;
    }
    finally {
      if (isset($ch) && is_resource($ch)) {
        curl_close($ch);
      }
    }
  }

  /**
   * Parse le HTML pour extraire les données.
   */
  private function parseHtml($html, $language = 'fr', $secteur = 'default') {
    $dom = new \DOMDocument();
    @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR);
    $xpath = new \DOMXPath($dom);

    $data = [];

    // Obtenir l'ID de div correct selon le secteur et la langue
    $divId = self::NEWS_DIV_IDS[$secteur][$language] ?? self::NEWS_DIV_IDS['default'][$language];
    
    \Drupal::logger('scraper_news')->debug('Using div ID: @divId for secteur: @secteur, language: @language', [
      '@divId' => $divId,
      '@secteur' => $secteur,
      '@language' => $language,
    ]);

    $articles = $xpath->query('//div[@id="' . $divId . '"]');

    foreach ($articles as $article) {
      // Extraction du titre
      $titleElement = $xpath->query('.//h1', $article)->item(0);
      $title = $titleElement ? trim($titleElement->textContent) : '';

      // Extraction de la date
      $dateElement = $xpath->query('.//span[@class="datearticle"]', $article)->item(0);
      $date = '';
      if ($dateElement) {
        $date = trim($dateElement->textContent);
        // Convertir la date du format dd.mm.yyyy au format d/m/Y
        $dateParts = explode('.', $date);
        if (count($dateParts) === 3) {
          $date = implode('/', $dateParts);
        }
      }

      // Extraction de l'image
      $imgElement = $xpath->query('.//div[@class="img_alaune_ineterne"]//img', $article)->item(0);
      $image = '';
      if ($imgElement) {
        $image = $imgElement->getAttribute('src');
      }

      // Extraction du contenu
      $bodyContent = '';

      // Récupérer le texte initial
      $chapitreElement = $xpath->query('.//div[@class="chapitre_pageinterne"]', $article)->item(0);
      if ($chapitreElement) {
        // Exclure le span de date du contenu
        $dateSpan = $xpath->query('.//span[@class="datearticle"]', $chapitreElement)->item(0);
        if ($dateSpan) {
          $dateSpan->parentNode->removeChild($dateSpan);
        }
        $bodyContent .= trim($chapitreElement->textContent);
      }

      // Récupérer le reste du contenu
      $resteElement = $xpath->query('.//div[@class="reste_contenu"]', $article)->item(0);
      if ($resteElement) {
        $bodyContent .= "\n" . trim($resteElement->textContent);
      }

      $data[] = [
        'title' => $title,
        'date' => $date,
        'body' => trim($bodyContent),
        'image' => $image,
      ];

      // Debug log
      \Drupal::logger('scraper_news')->debug('Parsed data: @data', [
        '@data' => print_r([
          'title' => $title,
          'date' => $date,
          'image' => $image,
          'body_length' => strlen($bodyContent),
        ], TRUE)
      ]);
    }

    return $data;
  }

  /**
   * Crée un nœud de type "Actualité" à partir des données scrapées.
   */
  private function createNewsNode($data, $language, $secteur = 'default') {
    try {
      \Drupal::logger('scraper_news')->debug('Creating node with secteur: @secteur for title: @title', [
        '@secteur' => $secteur,
        '@title' => $data['title'] ?? 'No title',
      ]);

      $node = Node::create([
        'type' => 'actualite',
        'title' => $data['title'],
        'body' => [
          'value' => $data['body'],
          'format' => 'full_html',
        ],
        'status' => 1,
        'langcode' => $language,
      ]);

      // Ajouter le secteur si ce n'est pas le secteur par défaut
      if ($secteur !== 'default') {
        \Drupal::logger('scraper_news')->debug('Trying to find secteur term for: @secteur', [
          '@secteur' => $secteur,
        ]);

        $secteur_term = $this->getSecteurTermBySecteur($secteur);
        if ($secteur_term) {
          \Drupal::logger('scraper_news')->debug('Found secteur term: @name (ID: @id)', [
            '@name' => $secteur_term->label(),
            '@id' => $secteur_term->id(),
          ]);
          $node->set('field_secteur', ['target_id' => $secteur_term->id()]);
        } else {
          \Drupal::logger('scraper_news')->warning('No secteur term found for: @secteur', [
            '@secteur' => $secteur,
          ]);
        }
      }

      if (!empty($data['date'])) {
        // La date est maintenant au format d/m/Y
        $date = \DateTime::createFromFormat('d/m/Y', $data['date']);
        if ($date) {
          $node->set('field_date', $date->format('Y-m-d'));
        } else {
          \Drupal::logger('scraper_news')->warning('Invalid date format: @date', [
            '@date' => $data['date'],
          ]);
        }
      }

      if (!empty($data['image'])) {
        try {
          $image_url = $data['image'];
          if (!str_starts_with($image_url, 'http')) {
            $image_url = 'https://www.transport.gov.ma' . $image_url;
          }

          \Drupal::logger('scraper_news')->debug('Attempting to download image from: @url', [
            '@url' => $image_url
          ]);

          // Vérifier si l'image existe avant de la télécharger
          $headers = get_headers($image_url, 1);
          if (!$headers || strpos($headers[0], '404') !== false) {
            \Drupal::logger('scraper_news')->warning('Image not found (404): @url', [
              '@url' => $image_url
            ]);
            // Laisser le champ image vide pour les images 404
          } else {
            $file_data = file_get_contents($image_url);
            if ($file_data) {
            $file_name = basename(parse_url($image_url, PHP_URL_PATH));
            $directory = 'public://actualites/';
            
            // Vérifier si le répertoire existe et est accessible en écriture
            if (!$this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS)) {
              throw new \Exception("Cannot create or write to directory: $directory");
            }
            
            $file = $this->fileRepository->writeData(
              $file_data,
              $directory . $file_name,
              FileSystemInterface::EXISTS_REPLACE
            );
            
            if ($file) {
              \Drupal::logger('scraper_news')->debug('File saved successfully: @file', [
                '@file' => $file->getFileUri()
              ]);
              
              $media = Media::create([
                'bundle' => 'image',
                'name' => $data['title'],
                'field_media_image' => [
                  'target_id' => $file->id(),
                  'alt' => $data['title'],
                  'title' => $data['title']
                ],
                'status' => 1,
                'langcode' => $language,
              ]);
              $media->save();
              
              $node->set('field_image_media', [
                'target_id' => $media->id()
              ]);
              
              \Drupal::logger('scraper_news')->debug('Media entity created with ID: @id', [
                '@id' => $media->id()
              ]);
            } else {
              throw new \Exception('Failed to save file');
            }
            } else {
              throw new \Exception('Failed to download image');
            }
          }
        }
        catch (\Exception $e) {
          \Drupal::logger('scraper_news')->error('Error processing image: @message', [
            '@message' => $e->getMessage(),
            '@url' => $image_url ?? 'unknown'
          ]);
        }
      }

      $node->save();
      return $node;
    }
    catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error creating news node: @message', [
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * Batch finished callback.
   */
  public function batchFinished($success, $results, $operations) {
    if ($success) {
      $message = $this->t('Import terminé. @success articles importés, @skipped ignorés.', [
        '@success' => $results['success'] ?? 0,
        '@skipped' => $results['skipped'] ?? 0,
      ]);
      \Drupal::messenger()->addMessage($message);
    } else {
      \Drupal::messenger()->addError($this->t('Une erreur est survenue pendant l\'import.'));
    }
  }
}
